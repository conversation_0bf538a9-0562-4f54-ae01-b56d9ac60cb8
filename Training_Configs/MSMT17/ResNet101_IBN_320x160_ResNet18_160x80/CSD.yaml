EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True

OUTPUT_DIR:
  ROOT_PATH: "./log/CSD"
  EXPERIMENT_NAME: "MSMT17_CSD_ResNet101_IBN_320x160_ResNet18_160x80"


DATASETS:
  NAMES: "MSMT17"
  ROOT_DIR: ""


DISTILLER:
  TYPE: "CSD"
  TEACHER_NAME: "ResNet101_ibn_a"
  TEACHER_MODEL_PATH: "./AIR_Distiller/download_ckpts/MSMT17_Teachers/MSMT17_ResNet101_IBN_320x160_64.13_84.49.pth"
  STUDENT_NAME: "ResNet18"
  STUDENT_PRETRAIN_PATH: ''

CSD:
  TOPK: 256
  TEMPERATURE_QUERY: 1
  TEMPERATURE_GALLERY: 0.01
  KD_WEIGHT: 5.0


INPUT:
  STUDENT_SIZE_TRAIN: [160, 80]
  STUDENT_SIZE_TEST: [160, 80]
  STUDENT_PADDING: 4

  TEACHER_SIZE_TRAIN: [320, 160]
  TEACHER_SIZE_TEST: [320, 160]

  TEACHER_PADDING: 8 
  RE_PROB: 0.5

DATALOADER:
  NUM_WORKERS: 8
  NUM_INSTANCE: 6

SOLVER:
  TRAINER: "kd"
  IMS_PER_BATCH: 96
  IMS_DISTILLATION_PER_BATCH: 256
  MAX_EPOCHS: 120
  BASE_LR: 0.01
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR" #"WarmupMultiStepLR"  #WarmupCosineAnnealingLR
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  LR_DECAY_STEPS: [40] #[30, 60, 90] #[30, 60, 90]
  OPTIMIZER_NAME: "SGD"

  CHECKPOINT_PERIOD: 120


TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 120
