EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True

OUTPUT_DIR:
  ROOT_PATH: "./log/ROP"
  EXPERIMENT_NAME: "SOP_ROP_ResNet101_256x256_MobileNetV3_Small_64x64"


DATASETS:
  NAMES: "SOP"
  ROOT_DIR: ""


DISTILLER:
  TYPE: "ROP"
  TEACHER_NAME: "ResNet101"
  TEACHER_MODEL_PATH: "./AIR_Distiller/download_ckpts/SOP_Teachers/SOP_ResNet101_256x256_72.42_87.13.pth"
  STUDENT_NAME: "MobileNetV3_Small"
  STUDENT_PRETRAIN_PATH: ''

ROP:
  TOPK: 256
  TEMPERATURE: 0.1
  RANK_WEIGHT: 0.2
  KD_WEIGHT: 2.0

INPUT:
  STUDENT_SIZE_TRAIN: [64, 64]
  STUDENT_SIZE_TEST: [64, 64]
  STUDENT_PADDING: 2

  TEACHER_SIZE_TRAIN: [256, 256]
  TEACHER_SIZE_TEST: [256, 256]

  TEACHER_PADDING: 8 
  RE_PROB: 0.5

DATALOADER:
  NUM_WORKERS: 8
  NUM_INSTANCE: 6

SOLVER:
  TRAINER: "kd"
  IMS_PER_BATCH: 96
  IMS_DISTILLATION_PER_BATCH: 256
  MAX_EPOCHS: 120
  BASE_LR: 0.01
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR" #"WarmupMultiStepLR"  #WarmupCosineAnnealingLR
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  LR_DECAY_STEPS: [40] #[30, 60, 90] #[30, 60, 90]
  OPTIMIZER_NAME: "SGD"

  CHECKPOINT_PERIOD: 120


TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 120
