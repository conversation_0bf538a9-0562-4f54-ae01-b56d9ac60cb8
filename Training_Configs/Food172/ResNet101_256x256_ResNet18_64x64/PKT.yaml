EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True

OUTPUT_DIR:
  ROOT_PATH: "./log/ResNet101_ResNet18/PKT"
  EXPERIMENT_NAME: "Food172_PKT_ResNet101_256x256_ResNet18_64x64"


DATASETS:
  NAMES: "Food172"
  ROOT_DIR: "/22zhangshijie/datasets/food-172/food172"


DISTILLER:
  TYPE: "PKT"
  TEACHER_NAME: "ResNet101"
  TEACHER_MODEL_PATH: "/22zhangshijie/zsj/D3till/D3still-main1/log/teacher_food172/Food172_ResNet101_256x256/NONE_90.pth"
  
  STUDENT_NAME: "ResNet18"
  STUDENT_PRETRAIN_PATH: ''

PKT:
  KD_WEIGHT: 30000.0

INPUT:
  STUDENT_SIZE_TRAIN: [64, 64]
  STUDENT_SIZE_TEST: [64, 64]
  STUDENT_PADDING: 2

  TEACHER_SIZE_TRAIN: [256, 256]
  TEACHER_SIZE_TEST: [256, 256]

  TEACHER_PADDING: 8 
  RE_PROB: 0.5

DATALOADER:
  NUM_WORKERS: 4
  NUM_INSTANCE: 6

SOLVER:
  TRAINER: "kd"
  IMS_PER_BATCH: 96
  IMS_DISTILLATION_PER_BATCH: 256
  MAX_EPOCHS: 120
  BASE_LR: 0.01
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR" #"WarmupMultiStepLR"  #WarmupCosineAnnealingLR
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  LR_DECAY_STEPS: [40] #[30, 60, 90] #[30, 60, 90]
  OPTIMIZER_NAME: "SGD"

  CHECKPOINT_PERIOD: 120
  SEED: 42


TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 120