EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True
  CE_LOSS_WEIGHT: 1.0            # ✅ 添加：明确使用 CE loss
  TRIPLET_LOSS_WEIGHT: 0.0       # ✅ 添加：关闭 Triplet loss

OUTPUT_DIR:
  ROOT_PATH: "./log/teacher_food101"
  EXPERIMENT_NAME: "Food101_ResNet101_256x256"

DATASETS:
  NAMES: "Food101"
  ROOT_DIR: "/22zhangshijie/food101/food-101"

DISTILLER:
  TYPE: "NONE"                   # ✅ 教师模型训练必须设为 NONE
  STUDENT_NAME: "ResNet101"
  STUDENT_PRETRAIN_PATH: ""
  STUDENT_LAST_STRIDE: 2

INPUT:
  STUDENT_SIZE_TRAIN: [256, 256]
  STUDENT_SIZE_TEST: [256, 256]
  STUDENT_PADDING: 8
  RE_PROB: 0.5
  PIXEL_MEAN: [0.485, 0.456, 0.406]
  PIXEL_STD: [0.229, 0.224, 0.225]
  PROB: 0.5

DATALOADER:
  NUM_WORKERS: 4
  NUM_INSTANCE: 6                # ✅ 保留无妨
  SAMPLER: "random"              # ✅ 合理

SOLVER:
  TRAINER: "vanilla"             # ✅ 无蒸馏时应为 vanilla（非 kd）
  BASE_LR: 0.01
  MAX_EPOCHS: 90
  IMS_PER_BATCH: 96
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR"
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  OPTIMIZER_NAME: "SGD"
  CHECKPOINT_PERIOD: 90
  SEED: 2024

TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 90