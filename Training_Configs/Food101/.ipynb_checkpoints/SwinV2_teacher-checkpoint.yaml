EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True
  CE_LOSS_WEIGHT: 1.0
  TRIPLET_LOSS_WEIGHT: 0.0

OUTPUT_DIR:
  ROOT_PATH: "./log/teacher_food101"
  EXPERIMENT_NAME: "Food101_SwinV2_small_256x256"

DATASETS:
  NAMES: "Food101"
  ROOT_DIR: "/22zhangshijie/food101/food-101"

DISTILLER:
  TYPE: "NONE"                      # 教师训练必须 NONE
  STUDENT_NAME: "Swin_Transformer_V2_Small"      # ✅ 使用 SwinV2_small
  STUDENT_PRETRAIN_PATH: "/22zhangshijie/checkpoint/pytorch_model.bin"
  STUDENT_LAST_STRIDE: 2            # 保留一致设置

INPUT:
  STUDENT_SIZE_TRAIN: [256, 256]
  STUDENT_SIZE_TEST: [256, 256]
  STUDENT_PADDING: 8
  RE_PROB: 0.5
  PIXEL_MEAN: [0.485, 0.456, 0.406]
  PIXEL_STD: [0.229, 0.224, 0.225]
  PROB: 0.5

DATALOADER:
  NUM_WORKERS: 4
  NUM_INSTANCE: 6
  SAMPLER: "random"

SOLVER:
  TRAINER: "vanilla"
  BASE_LR: 0.01
  MAX_EPOCHS: 90
  IMS_PER_BATCH: 96
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR"
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  OPTIMIZER_NAME: "SGD"             # 和 ResNet 保持一致
  CHECKPOINT_PERIOD: 90
  SEED: 2024

TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 90