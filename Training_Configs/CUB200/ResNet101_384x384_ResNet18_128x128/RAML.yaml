EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True

OUTPUT_DIR:
  ROOT_PATH: "./log/RAML"
  EXPERIMENT_NAME: "CUB200_RAML_ResNet101_384x284_ResNet18_128x128"


DATASETS:
  NAMES: "CUB200"
  ROOT_DIR: ""


DISTILLER:
  TYPE: "RAML"
  TEACHER_NAME: "ResNet101"
  TEACHER_MODEL_PATH: "./AIR_Distiller/download_ckpts/CUB200_Teachers/CUB200_ResNet101_384x384_76.53_85.05.pth"
  STUDENT_NAME: "ResNet18"
  STUDENT_PRETRAIN_PATH: ''

RAML:
  LAMBDA1: 0.7482
  LAMBDA2: 0.6778
  KD_WEIGHT: 10.0


INPUT:
  STUDENT_SIZE_TRAIN: [128, 128]
  STUDENT_SIZE_TEST: [128, 128]
  STUDENT_PADDING: 4

  TEACHER_SIZE_TRAIN: [384, 384]
  TEACHER_SIZE_TEST: [384, 384]

  TEACHER_PADDING: 12  
  RE_PROB: 0.0

DATALOADER:
  NUM_WORKERS: 8
  NUM_INSTANCE: 6

SOLVER:
  TRAINER: "kd"
  IMS_PER_BATCH: 96
  IMS_DISTILLATION_PER_BATCH: 256
  MAX_EPOCHS: 120
  BASE_LR: 0.01
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR" #"WarmupMultiStepLR"  #WarmupCosineAnnealingLR
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  LR_DECAY_STEPS: [40] #[30, 60, 90] #[30, 60, 90]
  OPTIMIZER_NAME: "SGD"

  CHECKPOINT_PERIOD: 120


TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 120
