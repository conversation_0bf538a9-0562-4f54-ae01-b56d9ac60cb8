EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True

OUTPUT_DIR:
  ROOT_PATH: "./log/FitNet"
  EXPERIMENT_NAME: "CUB200_FitNet_Swin_Transformer_V2_Small_256x256_ResNet18_128x128"


DATASETS:
  NAMES: "CUB200"
  ROOT_DIR: ""


DISTILLER:
  TYPE: "FitNet"
  TEACHER_NAME: "Swin_Transformer_V2_Small"
  TEACHER_MODEL_PATH: "./AIR_Distiller/download_ckpts/CUB200_Teachers/CUB200_Swin_Transformer_Small_256x256_78.98_84.52.pth"
  STUDENT_NAME: "ResNet18"
  STUDENT_PRETRAIN_PATH: ''

FITNET:
  KD_WEIGHT: 2.0


INPUT:
  STUDENT_SIZE_TRAIN: [128, 128]
  STUDENT_SIZE_TEST: [128, 128]
  STUDENT_PADDING: 4

  TEACHER_SIZE_TRAIN: [256, 256]
  TEACHER_SIZE_TEST: [256, 256]

  TEACHER_PADDING: 8
  RE_PROB: 0.0

DATALOADER:
  NUM_WORKERS: 8
  NUM_INSTANCE: 6

SOLVER:
  TRAINER: "kd"
  IMS_PER_BATCH: 96
  IMS_DISTILLATION_PER_BATCH: 256
  MAX_EPOCHS: 120
  BASE_LR: 0.01
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR" #"WarmupMultiStepLR"  #WarmupCosineAnnealingLR
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  LR_DECAY_STEPS: [40] #[30, 60, 90] #[30, 60, 90]
  OPTIMIZER_NAME: "SGD"

  CHECKPOINT_PERIOD: 120


TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 120
