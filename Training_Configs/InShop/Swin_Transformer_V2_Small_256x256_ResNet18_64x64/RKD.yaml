EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True

OUTPUT_DIR:
  ROOT_PATH: "./log/RKD"
  EXPERIMENT_NAME: "InShop_RKD_Swin_Transformer_V2_Small_256x256_ResNet18_64x64"


DATASETS:
  NAMES: "InShop"
  ROOT_DIR: ""


DISTILLER:
  TYPE: "RKD"
  TEACHER_NAME: "Swin_Transformer_V2_Small"
  TEACHER_MODEL_PATH: "./AIR_Distiller/download_ckpts/InShop_Teachers/InShop_Swin_Transformer_Small_256x256_81.19_95.20.pth"
  STUDENT_NAME: "ResNet18"
  STUDENT_PRETRAIN_PATH: ''

RKD:
  KD_WEIGHT: 1.0


INPUT:
  STUDENT_SIZE_TRAIN: [64, 64]
  STUDENT_SIZE_TEST: [64, 64]
  STUDENT_PADDING: 2

  TEACHER_SIZE_TRAIN: [256, 256]
  TEACHER_SIZE_TEST: [256, 256]

  TEACHER_PADDING: 8 
  RE_PROB: 0.5

DATALOADER:
  NUM_WORKERS: 8
  NUM_INSTANCE: 6

SOLVER:
  TRAINER: "kd"
  IMS_PER_BATCH: 96
  IMS_DISTILLATION_PER_BATCH: 256
  MAX_EPOCHS: 120
  BASE_LR: 0.01
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR" #"WarmupMultiStepLR"  #WarmupCosineAnnealingLR
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  LR_DECAY_STEPS: [40] #[30, 60, 90] #[30, 60, 90]
  OPTIMIZER_NAME: "SGD"

  CHECKPOINT_PERIOD: 120


TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 120
