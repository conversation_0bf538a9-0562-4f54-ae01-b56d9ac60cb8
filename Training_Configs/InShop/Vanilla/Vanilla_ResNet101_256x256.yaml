EXPERIMENT:
  DEVICE_ID: "'0'"
  CUDA_AMP: True

OUTPUT_DIR:
  ROOT_PATH: "./log"
  EXPERIMENT_NAME: "InShop_ResNet101_256x256"
  


DATASETS:
  NAMES: "InShop"
  ROOT_DIR: "/home/<USER>/xieyi/data"
  

DISTILLER:
  TYPE: "NONE"
  STUDENT_NAME: "ResNet101"
  STUDENT_PRETRAIN_PATH: ''

INPUT:
  STUDENT_SIZE_TRAIN: [256, 256]
  STUDENT_SIZE_TEST: [256, 256]
  STUDENT_PADDING: 8
  RE_PROB: 0.5

DATALOADER:
  NUM_WORKERS: 8
  NUM_INSTANCE: 6

SOLVER:
  TRAINER: "vanilla"
  IMS_PER_BATCH: 96
  MAX_EPOCHS: 120
  BASE_LR: 0.01
  LR_DECAY_TYPE: "WarmupCosineAnnealingLR" #"WarmupMultiStepLR"  #WarmupCosineAnnealingLR
  LR_WARMUP_EPOCHS: 10
  LR_WARMUP_FACTOR: 0.1
  LR_DECAY_STEPS: [40] #[30, 60, 90] 
  OPTIMIZER_NAME: "SGD"

  CHECKPOINT_PERIOD: 120


TEST:
  IMS_PER_BATCH: 512
  WEIGHT: 120