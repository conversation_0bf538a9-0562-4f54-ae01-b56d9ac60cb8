# tools/generate_fixed_split.py
from dataloader.datasets.Food101 import Food101

# 初始化 Food101 实例（你需要确保路径对得上）
dataset = Food101(root='/22zhangshijie/food101/food-101', verbose=False)

# 使用原始方式生成一次划分
query_list, gallery_list = dataset._split_test_set()

# 保存成固定文件
with open('/22zhangshijie/food101/food-101/meta/query_fixed.txt', 'w') as f:
    f.writelines([q + '\n' for q in query_list])

with open('/22zhangshijie/food101/food-101/meta/gallery_fixed.txt', 'w') as f:
    f.writelines([g + '\n' for g in gallery_list])

print("✅ 固定划分文件已生成")