from ._base import Vanilla
from .KD import VanillaKD
from .FitNet import <PERSON>t<PERSON>
from .CC import CC
from .RKD import RKD
from .PKT import PKT
from .CSD import CSD
from .ROP import ROP
from .RAML import RAML
from .D3 import D3
from .SemCKD import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .SelfAt import SelfA
from .our import Our

distiller_dict = {
    "NONE": Vanilla,
    "VanillaKD": VanillaKD,
    "FitNet": FitNet,
    "CC": CC,
    "RKD": RKD,
    "PKT": PKT,
    "CSD": CSD,
    "ROP": ROP,
    "RAML": RAML,
    "D3": D3,
    "Our": Our
}
