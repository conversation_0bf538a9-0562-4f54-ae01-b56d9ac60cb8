


import torch
import torch.nn as nn
import torch.nn.functional as F

class UGDFeatureDistill(nn.Module):
    def __init__(self, student_dims, teacher_dims, proj_dim=256, use_bn=True):
        super().__init__()
        self.proj_s = nn.ModuleList([
            nn.Sequential(
                nn.Linear(dim, proj_dim),
                nn.BatchNorm1d(proj_dim) if use_bn else nn.Identity(),
                nn.ReLU(inplace=True)
            ) for dim in student_dims
        ])
        self.proj_t = nn.ModuleList([
            nn.Sequential(
                nn.Linear(dim, proj_dim),
                nn.BatchNorm1d(proj_dim) if use_bn else nn.Identity(),
                nn.ReLU(inplace=True)
            ) for dim in teacher_dims
        ])
        self.loss_fn = nn.MSELoss()

    def forward(self, stu_feats, tea_feats):
        loss = 0.
        for i, (s_feat, t_feat) in enumerate(zip(stu_feats, tea_feats)):
            s = F.adaptive_avg_pool2d(s_feat, 1).flatten(1)
            t = F.adaptive_avg_pool2d(t_feat, 1).flatten(1)
            loss += self.loss_fn(self.proj_s[i](s), self.proj_t[i](t))
        return loss / len(stu_feats)
