# import torch
# import torch.nn as nn
# import torch.nn.functional as F
# from ._base import Distiller
# from .SemCKD import SemC<PERSON><PERSON><PERSON>oss
# from .SelfAt import SelfA


# def d3_loss(student_features, teacher_features, topk, alpha, beta, gamma): 

#     batch_size = student_features.shape[0]

#     # Normalize student and teacher features
#     student_features = F.normalize(student_features, p=2, dim=1)
#     teacher_features = F.normalize(teacher_features, p=2, dim=1)

#     # Compute similarity matrices
#     teacher_similarity = teacher_features.double().mm(teacher_features.double().t())
#     cross_similarity = student_features.double().mm(teacher_features.double().t())

#     # Get sorted teacher similarity and corresponding cross similarity
#     teacher_topk_values, sorted_indices = torch.sort(teacher_similarity, dim=1, descending=True)
#     student_topk_values = torch.gather(cross_similarity, 1, sorted_indices)

#     # Feature distillation loss (alpha term)
#     fd_loss = alpha * torch.norm(teacher_topk_values[:, 0] - student_topk_values[:, 0], p=2) / batch_size

#     # Extract top-k similarities (excluding the highest)
#     student_distances = student_topk_values[:, 1:topk]
#     teacher_distances = teacher_topk_values[:, 1:topk]
   
    
#     # Compute pairwise difference matrices
#     student_diff_matrix = student_distances.unsqueeze(1) - student_distances.unsqueeze(2)
#     teacher_diff_matrix = teacher_distances.unsqueeze(1) - teacher_distances.unsqueeze(2)

#     # Flatten the difference matrices
#     student_diff_flat = student_diff_matrix.view(batch_size, -1)
#     teacher_diff_flat = teacher_diff_matrix.view(batch_size, -1)
    
#     # Avoid division by zero
#     teacher_diff_flat[teacher_diff_flat == 0] = 1

#     # Compute hard and simple weights
#     hard_weights = (student_diff_flat / teacher_diff_flat).detach()
#     simple_weights = (student_diff_flat / teacher_diff_flat).detach()

#     hard_weights[hard_weights >= 0] = 0
#     hard_weights[hard_weights < 0] = 1

#     simple_weights[simple_weights <= 0] = 0
#     simple_weights[simple_weights > 0] = 1

#     # Avoid division by zero in student differences
#     student_diff_flat[student_diff_flat == 0] = 1

#     # Compute weighted result matrices
#     hard_loss_matrix = hard_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))
#     simple_loss_matrix = simple_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))

#     # Relation KD loss (beta and gamma terms)
#     hard_rd_loss = beta * torch.mean(torch.norm(hard_loss_matrix, p=2, dim=1)) / (topk-1)

#     simple_rd_loss = gamma * torch.mean(torch.norm(simple_loss_matrix, p=2, dim=1)) / (topk-1)
    
#     return fd_loss +  hard_rd_loss + simple_rd_loss



# class D3(Distiller):
#     """D3still: Decoupled Differential Distillation for Asymmetric Image Retrieval. CVPR2024"""

#     def __init__(self, student, teacher, cfg):
#         super(D3, self).__init__(student, teacher, cfg)

#         self.topk = cfg.D3.TOPK
#         self.alpha = cfg.D3.ALPHA
#         self.beta = cfg.D3.BETA
#         self.gamma = cfg.D3.GAMMA       
#         self.sem_weight = cfg.D3.SEM_WEIGHT
#         self.kd_loss_weight = cfg.D3.KD_WEIGHT

#         self.sem_ckd_loss = SemCKDLoss()
#         self.self_attention = None  # lazy init in forward

#     def forward_train(self, image, kd_student_image, kd_teacher_image, target, kd_target, **kwargs):
#         logits_student, feature_student = self.student(image)
#         ce_loss = self.ce_loss_weight * self.ce_loss(logits_student, target)
#         triplet_loss = self.tri_loss_weight * self.triplet_loss(feature_student["pooled_feat"], target)

#         _, kd_feature_student = self.student(kd_student_image)
#         _, kd_feature_teacher = self.teacher(kd_teacher_image)

#         kd_loss = self.kd_loss_weight * d3_loss(
#             kd_feature_student["retrieval_feat"], 
#             kd_feature_teacher["retrieval_feat"],
#             self.topk,
#             self.alpha,
#             self.beta,
#             self.gamma
#         )

#         # 取中间几层做 SemCKD
#         stu_feats = kd_feature_student["feats"][1:-1]
#         tea_feats = kd_feature_teacher["feats"][1:-1]

#         # 初始化 attention 模块（一次即可）
#         if self.self_attention is None:
#             s_n = [f.shape[1] for f in stu_feats]
#             t_n = [f.shape[1] for f in tea_feats]
#             input_channel = s_n[1]  # 用 layer2 的通道数即可
#             self.self_attention = SelfA(
#                 s_len=len(stu_feats),
#                 t_len=len(tea_feats),
#                 input_channel=input_channel,
#                 s_n=s_n,
#                 s_t=t_n
#             )
#             self.self_attention.to(image.device)  # 加这句确保模型放到 GPU 上

#         proj_stu, proj_tea, attn_weights = self.self_attention(stu_feats, tea_feats)
#         semckd_loss = self.sem_weight * self.sem_ckd_loss(proj_stu, proj_tea, attn_weights)

#         losses_dict = {
#             "loss_ce": ce_loss,
#             "loss_triplet": triplet_loss,
#             "loss_kd": kd_loss,
#             "loss_semckd": semckd_loss,
#         }
#         return logits_student, losses_dict


# import torch
# import torch.nn as nn
# import torch.nn.functional as F
# from ._base import Distiller
# from .SemCKD import SemCKDLoss
# from .SelfAt import SelfA


# def d3_loss(student_features, teacher_features, topk, alpha, beta, gamma): 

#     batch_size = student_features.shape[0]

#     # Normalize student and teacher features
#     student_features = F.normalize(student_features, p=2, dim=1)
#     teacher_features = F.normalize(teacher_features, p=2, dim=1)

#     # Compute similarity matrices
#     teacher_similarity = teacher_features.double().mm(teacher_features.double().t())
#     cross_similarity = student_features.double().mm(teacher_features.double().t())

#     # Get sorted teacher similarity and corresponding cross similarity
#     teacher_topk_values, sorted_indices = torch.sort(teacher_similarity, dim=1, descending=True)
#     student_topk_values = torch.gather(cross_similarity, 1, sorted_indices)

#     # Feature distillation loss (alpha term)
#     fd_loss = alpha * torch.norm(teacher_topk_values[:, 0] - student_topk_values[:, 0], p=2) / batch_size

#     # Extract top-k similarities (excluding the highest)
#     student_distances = student_topk_values[:, 1:topk]
#     teacher_distances = teacher_topk_values[:, 1:topk]
   
    
#     # Compute pairwise difference matrices
#     student_diff_matrix = student_distances.unsqueeze(1) - student_distances.unsqueeze(2)
#     teacher_diff_matrix = teacher_distances.unsqueeze(1) - teacher_distances.unsqueeze(2)

#     # Flatten the difference matrices
#     student_diff_flat = student_diff_matrix.view(batch_size, -1)
#     teacher_diff_flat = teacher_diff_matrix.view(batch_size, -1)
    
#     # Avoid division by zero
#     teacher_diff_flat[teacher_diff_flat == 0] = 1

#     # Compute hard and simple weights
#     hard_weights = (student_diff_flat / teacher_diff_flat).detach()
#     simple_weights = (student_diff_flat / teacher_diff_flat).detach()

#     hard_weights[hard_weights >= 0] = 0
#     hard_weights[hard_weights < 0] = 1

#     simple_weights[simple_weights <= 0] = 0
#     simple_weights[simple_weights > 0] = 1

#     # Avoid division by zero in student differences
#     student_diff_flat[student_diff_flat == 0] = 1

#     # Compute weighted result matrices
#     hard_loss_matrix = hard_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))
#     simple_loss_matrix = simple_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))

#     # Relation KD loss (beta and gamma terms)
#     hard_rd_loss = beta * torch.mean(torch.norm(hard_loss_matrix, p=2, dim=1)) / (topk-1)

#     simple_rd_loss = gamma * torch.mean(torch.norm(simple_loss_matrix, p=2, dim=1)) / (topk-1)
    
#     return fd_loss +  hard_rd_loss + simple_rd_loss



# class D3(Distiller):
#     """D3still: Decoupled Differential Distillation for Asymmetric Image Retrieval. CVPR2024"""

#     def __init__(self, student, teacher, cfg):
#         super(D3, self).__init__(student, teacher, cfg)
#         stu_dims = [64, 128, 256, 512]
#         tea_dims = [256, 512, 1024, 2048]

#         self.topk = cfg.D3.TOPK
#         self.alpha = cfg.D3.ALPHA
#         self.beta = cfg.D3.BETA
#         self.gamma = cfg.D3.GAMMA
#         self.sem_weight = cfg.D3.SEM_WEIGHT  # 在 __init__ 中加


#         self.kd_loss_weight = cfg.D3.KD_WEIGHT
#         self.sem_ckd_loss = SemCKDLoss()
#         # self.self_attention = SelfA(
#         #     s_len=len(stu_dims),
#         #     t_len=len(tea_dims),
#         #     input_channel=stu_dims[-1],
#         #     s_n = stu_dims,
#         #     s_t = tea_dims
#         # )
#         self.self_attention = None  # lazy init in forward

#     def forward_train(self, image, kd_student_image, kd_teacher_image, target, kd_target, **kwargs):
#         logits_student, feature_student = self.student(image)
#         ce_loss = self.ce_loss_weight * self.ce_loss(logits_student, target)
#         triplet_loss = self.tri_loss_weight * self.triplet_loss(feature_student["pooled_feat"], target)

#         # 获取蒸馏数据
#         logits_teacher, kd_feature_teacher = self.teacher(kd_teacher_image)
#         _, kd_feature_student = self.student(kd_student_image)

#         # 🎯 仅对预测正确的样本做 D3 蒸馏
#         with torch.no_grad():
#             teacher_pred = logits_teacher.argmax(dim=1)
#             correct_mask = (teacher_pred == kd_target)

#         if correct_mask.sum() > 0:
#             student_feat = kd_feature_student["retrieval_feat"][correct_mask]
#             teacher_feat = kd_feature_teacher["retrieval_feat"][correct_mask]

#             kd_loss = self.kd_loss_weight * d3_loss(
#                 student_feat,
#                 teacher_feat,
#                 self.topk,
#                 self.alpha,
#                 self.beta,
#                 self.gamma
#             )
#         else:
#             kd_loss = torch.tensor(0.0, device=image.device)

#         stu_feats = kd_feature_student["feats"][1:-1]
#         # print("11")
#         tea_feats = kd_feature_teacher["feats"][1:-1]


#         if self.self_attention is None:
#             s_n = [f.shape[1] for f in stu_feats]
#             t_n = [f.shape[1] for f in tea_feats]
#             input_channel = s_n[1]  # 用第二层的通道即可
#             self.self_attention = SelfA(
#                 s_len=len(stu_feats),
#                 t_len=len(tea_feats),
#                 input_channel=input_channel,
#                 s_n=s_n,
#                 s_t=t_n
#             ).to(image.device)

#         #proj_stu, proj_tea, attn_weights = self.self_attention(stu_feats, tea_feats)
#         #semckd_loss = self.sem_weight * self.sem_ckd_loss(proj_stu, proj_tea, attn_weights)
#         semckd_loss = torch.tensor(0.0, device=image.device)


#         losses_dict = {
#             "loss_ce": ce_loss,
#             "loss_triplet": triplet_loss,
#             "loss_kd": kd_loss,
#             "loss_semckd": semckd_loss,
#         }

#         return logits_student, losses_dict

#跑ResNet当老师
import torch
import torch.nn as nn
import torch.nn.functional as F
from ._base import Distiller

def d3_loss(student_features, teacher_features, topk, alpha, beta, gamma): 

    batch_size = student_features.shape[0]

    # Normalize student and teacher features
    student_features = F.normalize(student_features, p=2, dim=1)
    teacher_features = F.normalize(teacher_features, p=2, dim=1)

    # Compute similarity matrices
    teacher_similarity = teacher_features.double().mm(teacher_features.double().t())
    cross_similarity = student_features.double().mm(teacher_features.double().t())

    # Get sorted teacher similarity and corresponding cross similarity
    teacher_topk_values, sorted_indices = torch.sort(teacher_similarity, dim=1, descending=True)
    student_topk_values = torch.gather(cross_similarity, 1, sorted_indices)

    # Feature distillation loss (alpha term)
    fd_loss = alpha * torch.norm(teacher_topk_values[:, 0] - student_topk_values[:, 0], p=2) / batch_size

    # Extract top-k similarities (excluding the highest)
    student_distances = student_topk_values[:, 1:topk]
    teacher_distances = teacher_topk_values[:, 1:topk]
   
    
    # Compute pairwise difference matrices
    student_diff_matrix = student_distances.unsqueeze(1) - student_distances.unsqueeze(2)
    teacher_diff_matrix = teacher_distances.unsqueeze(1) - teacher_distances.unsqueeze(2)

    # Flatten the difference matrices
    student_diff_flat = student_diff_matrix.view(batch_size, -1)
    teacher_diff_flat = teacher_diff_matrix.view(batch_size, -1)
    
    # Avoid division by zero
    teacher_diff_flat[teacher_diff_flat == 0] = 1

    # Compute hard and simple weights
    hard_weights = (student_diff_flat / teacher_diff_flat).detach()
    simple_weights = (student_diff_flat / teacher_diff_flat).detach()

    hard_weights[hard_weights >= 0] = 0
    hard_weights[hard_weights < 0] = 1

    simple_weights[simple_weights <= 0] = 0
    simple_weights[simple_weights > 0] = 1

    # Avoid division by zero in student differences
    student_diff_flat[student_diff_flat == 0] = 1

    # Compute weighted result matrices
    hard_loss_matrix = hard_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))
    simple_loss_matrix = simple_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))

    # Relation KD loss (beta and gamma terms)
    hard_rd_loss = beta * torch.mean(torch.norm(hard_loss_matrix, p=2, dim=1)) / (topk-1)

    simple_rd_loss = gamma * torch.mean(torch.norm(simple_loss_matrix, p=2, dim=1)) / (topk-1)
    
    return fd_loss +  hard_rd_loss + simple_rd_loss



class D3(Distiller):
    """D3still: Decoupled Differential Distillation for Asymmetric Image Retrieval. CVPR2024"""

    def __init__(self, student, teacher, cfg):
        super(D3, self).__init__(student, teacher, cfg)

        self.topk = cfg.D3.TOPK
        self.alpha = cfg.D3.ALPHA
        self.beta = cfg.D3.BETA
        self.gamma = cfg.D3.GAMMA

        self.kd_loss_weight = cfg.D3.KD_WEIGHT

    def forward_train(self, image, kd_student_image, kd_teacher_image, target, kd_target, **kwargs):

        logits_student, feature_student = self.student(image)
        ce_loss = self.ce_loss_weight * self.ce_loss(logits_student, target)
        triplet_loss = self.tri_loss_weight * self.triplet_loss(feature_student["pooled_feat"], target)

        _, kd_feature_student = self.student(kd_student_image)
        _, kd_feature_teacher = self.teacher(kd_teacher_image)
        

        

        kd_loss = self.kd_loss_weight * d3_loss(kd_feature_student["retrieval_feat"], 
                                                 kd_feature_teacher["retrieval_feat"],
                                                 self.topk,
                                                 self.alpha,
                                                 self.beta,
                                                 self.gamma)
        semckd_loss = torch.tensor(0.0, device=image.device)

        losses_dict = {
            "loss_ce": ce_loss,
            "loss_triplet": triplet_loss,
            "loss_kd": kd_loss,
            "loss_semckd": semckd_loss,
        }
        return logits_student, losses_dict










#跑swin

import torch
import torch.nn as nn
import torch.nn.functional as F
from ._base import Distiller
from .SemCKD import SemCKDLoss
from .SelfAt import SelfA
from .ugd_module import UGDFeatureDistill


def d3_loss(student_features, teacher_features, topk, alpha, beta, gamma): 
    batch_size = student_features.shape[0]

    student_features = F.normalize(student_features, p=2, dim=1)
    teacher_features = F.normalize(teacher_features, p=2, dim=1)

    teacher_similarity = teacher_features.double().mm(teacher_features.double().t())
    cross_similarity = student_features.double().mm(teacher_features.double().t())

    teacher_topk_values, sorted_indices = torch.sort(teacher_similarity, dim=1, descending=True)
    student_topk_values = torch.gather(cross_similarity, 1, sorted_indices)

    fd_loss = alpha * torch.norm(teacher_topk_values[:, 0] - student_topk_values[:, 0], p=2) / batch_size

    student_distances = student_topk_values[:, 1:topk]
    teacher_distances = teacher_topk_values[:, 1:topk]

    student_diff_matrix = student_distances.unsqueeze(1) - student_distances.unsqueeze(2)
    teacher_diff_matrix = teacher_distances.unsqueeze(1) - teacher_distances.unsqueeze(2)

    student_diff_flat = student_diff_matrix.view(batch_size, -1)
    teacher_diff_flat = teacher_diff_matrix.view(batch_size, -1)

    teacher_diff_flat[teacher_diff_flat == 0] = 1
    student_diff_flat[student_diff_flat == 0] = 1

    hard_weights = (student_diff_flat / teacher_diff_flat).detach()
    simple_weights = (student_diff_flat / teacher_diff_flat).detach()

    hard_weights[hard_weights >= 0] = 0
    hard_weights[hard_weights < 0] = 1

    simple_weights[simple_weights <= 0] = 0
    simple_weights[simple_weights > 0] = 1

    hard_loss_matrix = hard_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))
    simple_loss_matrix = simple_weights * ((student_diff_flat - teacher_diff_flat) / (0.1 + teacher_diff_flat.abs()))

    hard_rd_loss = beta * torch.mean(torch.norm(hard_loss_matrix, p=2, dim=1)) / (topk - 1)
    simple_rd_loss = gamma * torch.mean(torch.norm(simple_loss_matrix, p=2, dim=1)) / (topk - 1)

    return fd_loss + hard_rd_loss + simple_rd_loss


class D3(Distiller):
    """D3still: Decoupled Differential Distillation for Asymmetric Image Retrieval. CVPR2024"""

    def __init__(self, student, teacher, cfg):
        super(D3, self).__init__(student, teacher, cfg)
        self.cfg = cfg
        self.topk = cfg.D3.TOPK
        self.alpha = cfg.D3.ALPHA
        self.beta = cfg.D3.BETA
        self.gamma = cfg.D3.GAMMA
        self.sem_weight = cfg.D3.SEM_WEIGHT
        self.kd_loss_weight = cfg.D3.KD_WEIGHT
        self.proj_dim = cfg.D3.PROJ_DIM  # 然后用这个写法
        # self.sem_weight = cfg.D3.SEM_WEIGHT


    def forward_train(self, image, kd_student_image, kd_teacher_image, target, kd_target, **kwargs):
        logits_student, feature_student = self.student(image)
        ce_loss = self.ce_loss_weight * self.ce_loss(logits_student, target)
        triplet_loss = self.tri_loss_weight * self.triplet_loss(feature_student["pooled_feat"], target)

        logits_teacher, kd_feature_teacher = self.teacher(kd_teacher_image)
        _, kd_feature_student = self.student(kd_student_image)

        with torch.no_grad():
            teacher_pred = logits_teacher.argmax(dim=1)
            correct_mask = (teacher_pred == kd_target)

        if correct_mask.sum() > 0:
            student_feat = kd_feature_student["retrieval_feat"][correct_mask]
            teacher_feat = kd_feature_teacher["retrieval_feat"][correct_mask]

            kd_loss = self.kd_loss_weight * d3_loss(
                student_feat,
                teacher_feat,
                self.topk,
                self.alpha,
                self.beta,
                self.gamma
            )
        else:
            kd_loss = torch.tensor(0.0, device=image.device)
        _, kd_feature_student = self.student(kd_student_image)
        _, kd_feature_teacher = self.teacher(kd_teacher_image)
        

        

        kd_loss = self.kd_loss_weight * d3_loss(kd_feature_student["retrieval_feat"], 
                                                 kd_feature_teacher["retrieval_feat"],
                                                 self.topk,
                                                 self.alpha,
                                                 self.beta,
                                                 self.gamma)



        取最后两层特征
        stu_feats = kd_feature_student["feats"][-2:]
        tea_feats = kd_feature_teacher["feats"][-2:]

        # 自动适配 Swin 的 [B, L, C] → [B, C, H, W]
        if len(tea_feats[0].shape) == 3:
            tea_feats = [
                feat.permute(0, 2, 1).reshape(feat.size(0), feat.size(2), int(feat.size(1)**0.5), -1)
                for feat in tea_feats
            ]

        # 初始化 UGD 模块
        if not hasattr(self, 'ugd_distill'):
            s_dims = [f.shape[1] for f in stu_feats]
            t_dims = [f.shape[1] for f in tea_feats]
            self.ugd_distill = UGDFeatureDistill(s_dims, t_dims, proj_dim=self.proj_dim).to(image.device)

        sem_loss = self.sem_weight * self.ugd_distill(stu_feats, tea_feats)

        losses_dict = {
            "loss_ce": ce_loss,
            "loss_triplet": triplet_loss,
            "loss_kd": kd_loss,
            "loss_semckd": sem_loss,  # 实际上是 ugd loss
        }
        return logits_student, losses_dict
