import os
import shutil
import random

# Food172 原始数据集根目录 (通常按类别组织)
original_dataset_root = '/22zhangshijie/datasets/food-172/food172/vireoFood-172'

# 目标 InShop 风格数据集根目录
target_dataset_root = '/22zhangshijie/datasets/food-172/food172/new_Food-172'

# 划分比例 (例如: 80% 训练, 10% Query, 10% Gallery)
train_ratio = 0.8
query_ratio = 0.1
gallery_ratio = 0.1

# 创建目标目录
os.makedirs(os.path.join(target_dataset_root, 'train'), exist_ok=True)
os.makedirs(os.path.join(target_dataset_root, 'query'), exist_ok=True)
os.makedirs(os.path.join(target_dataset_root, 'gallery'), exist_ok=True)

# 遍历原始数据集的类别目录
for category_name in os.listdir(original_dataset_root):
    category_path = os.path.join(original_dataset_root, category_name)
    if not os.path.isdir(category_path):
        continue

    # 获取当前类别所有图像的路径
    image_files = [f for f in os.listdir(category_path) if f.endswith(('.jpg', '.jpeg', '.png'))]
    random.shuffle(image_files) # 打乱顺序

    # 计算每个集合的图像数量
    num_images = len(image_files)
    num_train = int(num_images * train_ratio)
    num_query = int(num_images * query_ratio)
    num_gallery = num_images - num_train - num_query # 剩余的作为 gallery

    # 划分图像并复制/链接到目标目录
    current_idx = 0

    # 训练集
    for i in range(num_train):
        src_path = os.path.join(category_path, image_files[current_idx])
        dst_path = os.path.join(target_dataset_root, 'train', f"{category_name}_{image_files[current_idx]}") # 可以用类别名+文件名来命名，确保唯一性
        shutil.copy(src_path, dst_path) # 或者使用 os.symlink 创建链接
        current_idx += 1

    # Query 集
    for i in range(num_query):
        src_path = os.path.join(category_path, image_files[current_idx])
        dst_path = os.path.join(target_dataset_root, 'query', f"{category_name}_{image_files[current_idx]}")
        shutil.copy(src_path, dst_path)
        current_idx += 1

    # Gallery 集
    for i in range(num_gallery):
        src_path = os.path.join(category_path, image_files[current_idx])
        dst_path = os.path.join(target_dataset_root, 'gallery', f"{category_name}_{image_files[current_idx]}")
        shutil.copy(src_path, dst_path)
        current_idx += 1

    print(f"Processed category '{category_name}': {num_train} train, {num_query} query, {num_gallery} gallery")

print("InShop-style Food172 dataset created.")
