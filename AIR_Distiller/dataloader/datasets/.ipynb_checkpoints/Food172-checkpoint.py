import glob
import re
import os.path as osp
from .bases import BaseImageDataset # 假设你有一个 BaseImageDataset 类

class Food172(BaseImageDataset):
    """
    Food172 dataset with InShop-style fixed train/query/gallery split.
    This split is manually created based on a pre-defined ratio per category.
    """

    dataset_dir = 'food172_inshop_style' # 指向你手动创建的 InShop 风格数据集目录

    def __init__(self, root='../', verbose=True, **kwargs):
        super(Food172InShopStyle, self).__init__()
        self.dataset_dir = osp.join(root, self.dataset_dir)
        self.train_dir = osp.join(self.dataset_dir, 'train')
        self.query_dir = osp.join(self.dataset_dir, 'query')
        self.gallery_dir = osp.join(self.dataset_dir, 'gallery')

        self._check_before_run()

        # Notice: relabel=True for train, relabel=False for query and gallery
        train = self._process_dir(self.train_dir, relabel=True)
        query = self._process_dir(self.query_dir, relabel=False)
        gallery = self._process_dir(self.gallery_dir, relabel=False)

        if verbose:
            print(f"=> Food172 (InShop Style) loaded from {self.dataset_dir}")
            self.print_dataset_statistics(train, query, gallery)

        self.train = train
        self.query = query
        self.gallery = gallery

        self.num_train_pids, self.num_train_imgs, self.num_train_cams = self.get_imagedata_info(self.train)
        self.num_query_pids, self.num_query_imgs, self.num_query_cams = self.get_imagedata_info(self.query)
        self.num_gallery_pids, self.num_gallery_imgs, self.num_gallery_cams = self.get_imagedata_info(self.gallery)

    def _check_before_run(self):
        """Check if all files are available before going deeper"""
        if not osp.exists(self.dataset_dir):
             raise RuntimeError("'{}' is not available".format(self.dataset_dir))
        if not osp.exists(self.train_dir):
            raise RuntimeError("'{}' is not available".format(self.train_dir))
        if not osp.exists(self.query_dir):
            raise RuntimeError("'{}' is not available".format(self.query_dir))
        if not osp.exists(self.gallery_dir):
            raise RuntimeError("'{}' is not available".format(self.gallery_dir))


    def _process_dir(self, dir_path, relabel=False):
        img_paths = glob.glob(osp.join(dir_path, '*.jpg')) # Adjust pattern if using other formats

        # Assuming filenames are in the format: category_name_original_filename.jpg
        # Need to extract category_name as PID
        pattern = re.compile(r'(.+)_img_(\d+)\.(jpg|jpeg|png)', re.IGNORECASE) # Adjust regex based on how you named files

        pid_container = set()
        dataset = []

        for img_path in img_paths:
            match = pattern.search(osp.basename(img_path))
            if not match:
                 print(f"Warning: Filename format mismatch for {osp.basename(img_path)}")
                 continue

            category_name = match.group(1)
            # Use category_name as PID
            pid = category_name

            pid_container.add(pid)

            # For InShop style, we can use 0 for train/query and 1 for gallery as 'camid'
            # Or simply 0 for all, as camid is not the primary concept here.
            # Let's use 0 for query and 1 for gallery, and 0 for train (consistent with some InShop examples)
            if 'train' in dir_path:
                 camid = 0
            elif 'query' in dir_path:
                 camid = 0
            elif 'gallery' in dir_path:
                 camid = 1
            else:
                 camid = -1 # Should not happen with correct paths


        # Create pid to label mapping if relabel is True
        if relabel:
            # Sort pids to ensure consistent mapping
            pid_list = sorted(list(pid_container))
            pid2label = {pid: label for label, pid in enumerate(pid_list)}
        else:
            # Use original PID (category name)
            pid2label = {pid: pid for pid in pid_container}


        # Re-process to build the dataset list
        for img_path in img_paths:
            match = pattern.search(osp.basename(img_path))
            if not match:
                 continue

            category_name = match.group(1)
            pid = category_name

            # Assign camid again based on directory
            if 'train' in dir_path:
                 camid = 0
            elif 'query' in dir_path:
                 camid = 0
            elif 'gallery' in dir_path:
                 camid = 1
            else:
                 camid = -1

            # Get the final PID (re-labeled or original)
            final_pid = pid2label[pid]

            dataset.append((img_path, final_pid, camid))


        print(f"Processed {len(img_paths)} images in {osp.basename(dir_path)}. Found {len(pid_container)} unique PIDs.")
        return dataset

