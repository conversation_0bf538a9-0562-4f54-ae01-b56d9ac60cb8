import os
import os.path as osp
import random
from collections import defaultdict
from .bases import BaseImageDataset

class Food256(BaseImageDataset):
    """
    Food256 Dataset Loader with SOP-style query/gallery split.
    """

    dataset_dir = 'UECFOOD256'

    def __init__(self, root='/22zhangshijie/datasets', verbose=True, split_ratio=0.5, **kwargs):
        super(Food256, self).__init__()
        self.dataset_dir = osp.join(root, self.dataset_dir)
        self.train_list = osp.join(self.dataset_dir, 'train_dir.txt')
        self.test_list = osp.join(self.dataset_dir, 'test_dir.txt')
        self.split_ratio = split_ratio  # Query占测试集的比例

        self._check_before_run()

        # 处理训练集
        train = self._process_list(self.train_list, relabel=True)

        # 处理测试集 - 真正的非对称划分
        query, gallery = self._create_asymmetric_split()

        if verbose:
            print("=> Food256 loaded (TRUE Asymmetric Retrieval Split)")
            print(f"   Training categories: {len(set([item[1] for item in train]))}")
            print(f"   Query categories: {len(set([item[1] for item in query]))}")
            print(f"   Gallery categories: {len(set([item[1] for item in gallery]))}")
            print(f"   Query/Gallery ratio: {len(query)}/{len(gallery)} = {len(query)/len(gallery):.2f}")
            print(f"   Asymmetric factor: Query uses {self.split_ratio*100:.0f}% of test images")
            self.print_dataset_statistics(train, query, gallery)

        self.train = train
        self.query = query
        self.gallery = gallery

        self.num_train_pids, self.num_train_imgs, self.num_train_cams = self.get_imagedata_info(train)
        self.num_query_pids, self.num_query_imgs, self.num_query_cams = self.get_imagedata_info(query)
        self.num_gallery_pids, self.num_gallery_imgs, self.num_gallery_cams = self.get_imagedata_info(gallery)

    def _check_before_run(self):
        if not osp.exists(self.dataset_dir):
            raise RuntimeError(f"{self.dataset_dir} not found")
        if not osp.exists(self.train_list):
            raise RuntimeError(f"{self.train_list} not found")
        if not osp.exists(self.test_list):
            raise RuntimeError(f"{self.test_list} not found")

    def _create_asymmetric_split(self):
        """创建真正的非对称检索划分"""
        # 读取测试数据
        pid_to_images = defaultdict(list)
        with open(self.test_list, 'r') as f:
            for line in f:
                img, pid = line.strip().split()
                pid_to_images[pid].append((img, pid))

        # 只保留至少有3张图片的类别（确保能够进行非对称划分）
        valid_categories = {pid: images for pid, images in pid_to_images.items() if len(images) >= 3}

        query_data = []
        gallery_data = []

        # 为每个类别创建非对称划分
        for pid, images in valid_categories.items():
            # 随机打乱图片顺序
            random.shuffle(images)

            # Query: 使用部分图片（模拟用户拍摄的低质量图片）
            query_count = max(1, int(len(images) * self.split_ratio))
            query_images = images[:query_count]

            # Gallery: 使用所有图片（模拟高质量参考图片库）
            gallery_images = images

            # 添加到对应列表，设置不同的camid
            for img, pid in query_images:
                query_data.append(f"{img} {pid}")

            for img, pid in gallery_images:
                gallery_data.append(f"{img} {pid}")

        # 转换为数据集格式
        query = self._process_list_from_list(query_data, relabel=False, camid=0)
        gallery = self._process_list_from_list(gallery_data, relabel=False, camid=1)

        return query, gallery

    def _process_list(self, txt_path, relabel=False):
        with open(txt_path, 'r') as f:
            lines = f.readlines()

        pid_container = set()
        for line in lines:
            img_rel_path = line.strip().split()[0]
            pid = line.strip().split()[1]
            pid_container.add(pid)
        pid2label = {pid: idx for idx, pid in enumerate(sorted(pid_container))}

        dataset = []
        for line in lines:
            img_rel_path, pid = line.strip().split()
            full_path = osp.join(self.dataset_dir, img_rel_path)
            pid = pid2label[pid] if relabel else int(pid)
            camid = 0
            dataset.append((full_path, pid, camid))
        return dataset

    def _process_list_from_list(self, lines, relabel=False, camid=0):
        pid_container = set()
        for line in lines:
            img_rel_path, pid = line.strip().split()
            pid_container.add(pid)

        pid2label = {pid: idx for idx, pid in enumerate(sorted(pid_container))}

        dataset = []
        for line in lines:
            img_rel_path, pid = line.strip().split()
            full_path = osp.join(self.dataset_dir, img_rel_path)
            pid = pid2label[pid] if relabel else int(pid)
            dataset.append((full_path, pid, camid))

        return dataset