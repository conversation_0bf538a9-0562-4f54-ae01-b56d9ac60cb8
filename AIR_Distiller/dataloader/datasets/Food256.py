import os
import os.path as osp
import random
from collections import defaultdict
from .bases import BaseImageDataset

class Food256(BaseImageDataset):
    """
    Food256 Dataset Loader with optimized SOP-style query/gallery split.

    Dataset details:
    - 256 food categories
    - SOP-style split: training categories and test categories are disjoint
    - Query and gallery use the same test images but with different camids
    - Suitable for large-scale food retrieval tasks
    """

    dataset_dir = 'UECFOOD256'

    def __init__(self, root='/22zhangshijie/datasets', verbose=True, **kwargs):
        super(Food256, self).__init__()
        self.dataset_dir = osp.join(root, self.dataset_dir)
        self.train_list = osp.join(self.dataset_dir, 'train_dir.txt')
        self.test_list = osp.join(self.dataset_dir, 'test_dir.txt')

        self._check_before_run()

        # 处理训练集
        train = self._process_list(self.train_list, relabel=True)

        # 处理测试集 - 优化的SOP风格划分
        test_data = self._process_test_data()
        query = self._create_query_gallery_split(test_data, camid=0)
        gallery = self._create_query_gallery_split(test_data, camid=1)

        if verbose:
            print("=> Food256 loaded (Optimized SOP-style split)")
            print(f"   Training categories: {len(set([item[1] for item in train]))}")
            print(f"   Test categories: {len(set([item[1] for item in query]))}")
            self.print_dataset_statistics(train, query, gallery)

        self.train = train
        self.query = query
        self.gallery = gallery

        self.num_train_pids, self.num_train_imgs, self.num_train_cams = self.get_imagedata_info(train)
        self.num_query_pids, self.num_query_imgs, self.num_query_cams = self.get_imagedata_info(query)
        self.num_gallery_pids, self.num_gallery_imgs, self.num_gallery_cams = self.get_imagedata_info(gallery)

    def _check_before_run(self):
        if not osp.exists(self.dataset_dir):
            raise RuntimeError(f"{self.dataset_dir} not found")
        if not osp.exists(self.train_list):
            raise RuntimeError(f"{self.train_list} not found")
        if not osp.exists(self.test_list):
            raise RuntimeError(f"{self.test_list} not found")

    def _process_test_data(self):
        """处理测试数据，确保每个类别至少有2张图片"""
        pid_to_lines = defaultdict(list)
        with open(self.test_list, 'r') as f:
            for line in f:
                img, pid = line.strip().split()
                pid_to_lines[pid].append(f"{img} {pid}")

        # 只保留至少有2张图片的类别
        valid_lines = []
        for pid, items in pid_to_lines.items():
            if len(items) >= 2:
                valid_lines.extend(items)

        return valid_lines

    def _create_query_gallery_split(self, test_lines, camid):
        """创建query/gallery划分"""
        return self._process_list_from_list(test_lines, relabel=False, camid=camid)

    def _process_list(self, txt_path, relabel=False):
        with open(txt_path, 'r') as f:
            lines = f.readlines()

        pid_container = set()
        for line in lines:
            img_rel_path = line.strip().split()[0]
            pid = line.strip().split()[1]
            pid_container.add(pid)
        pid2label = {pid: idx for idx, pid in enumerate(sorted(pid_container))}

        dataset = []
        for line in lines:
            img_rel_path, pid = line.strip().split()
            full_path = osp.join(self.dataset_dir, img_rel_path)
            pid = pid2label[pid] if relabel else int(pid)
            camid = 0
            dataset.append((full_path, pid, camid))
        return dataset

    def _process_list_from_list(self, lines, relabel=False, camid=0):
        pid_container = set()
        for line in lines:
            img_rel_path, pid = line.strip().split()
            pid_container.add(pid)

        pid2label = {pid: idx for idx, pid in enumerate(sorted(pid_container))}

        dataset = []
        for line in lines:
            img_rel_path, pid = line.strip().split()
            full_path = osp.join(self.dataset_dir, img_rel_path)
            pid = pid2label[pid] if relabel else int(pid)
            dataset.append((full_path, pid, camid))

        return dataset