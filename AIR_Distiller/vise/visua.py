import os
import sys
import cv2
import torch
import numpy as np
from torchvision import transforms
from pytorch_grad_cam import GradCAM
from pytorch_grad_cam.utils.image import show_cam_on_image
import matplotlib.pyplot as plt

# 添加项目路径以导入模型
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from AIR_Distiller.models import resnet18


def preprocess_image(img_path):
    raw_img = cv2.imread(img_path)[:, :, ::-1]
    rgb_img = cv2.resize(raw_img, (64, 64)).astype(np.float32) / 255.0
    tensor = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])(rgb_img)
    return rgb_img, tensor.unsqueeze(0)


def plot_cam(image_path, cam_path, flag=0):
    # 模型加载
    num_classes = 101
    ckpt_path = "/22zhangshijie/D3_120.pth"
    # ckpt_path = "../../log/ResNet101_ResNet18/PKT/Food101_D3_ResNet101_256x256_ResNet18_64x64/PKT_120.pth"
    model = resnet18(pretrained=False, num_classes=num_classes, last_stride=1)
    # state_dict = torch.load(ckpt_path, map_location="cpu")
    # model.load_state_dict(state_dict, strict=True)

    state_dict = torch.load(ckpt_path, map_location="cpu")
    # 仅保留 student 的参数并去掉前缀
    state_dict = {k.replace("student.", ""): v for k, v in state_dict.items() if k.startswith("student.")}
    model.load_state_dict(state_dict, strict=True)


    model.eval().cuda()
    

    target_layers = [model.layer2]

    rgb_img, input_tensor = preprocess_image(image_path)
    input_tensor = input_tensor.cuda()

    cam = GradCAM(model=model, target_layers=target_layers, use_cuda=True)

    # 模型预测并指定解释目标
    with torch.no_grad():
        output, _ = model(input_tensor)
        pred_class = output.argmax(dim=1).item()
        print("✅ Predicted class:", pred_class)

    grayscale_cam = cam(input_tensor=input_tensor)[0]
    print("🔥 grayscale_cam stats:", grayscale_cam.min(), grayscale_cam.max(), np.mean(grayscale_cam))

    # 修正激活值并归一化
    grayscale_cam = np.maximum(grayscale_cam, 0)
    grayscale_cam = grayscale_cam / (np.max(grayscale_cam) + 1e-8)

    grayscale_cam = 1 - grayscale_cam
    cam_image = show_cam_on_image(rgb_img, grayscale_cam, use_rgb=True, colormap=cv2.COLORMAP_JET)
    # cam_image = show_cam_on_image(rgb_img, grayscale_cam, use_rgb=True, colormap=cv2.COLORMAP_TURBO)
    # 创建输出目录
    if not os.path.exists(cam_path):
        os.makedirs(cam_path)

    filename = os.path.basename(image_path)
    # 保存原始图片（未叠加 CAM）
    orig_img_path = os.path.join(cam_path, filename)
    orig_img_rgb = np.uint8(rgb_img * 255)
    orig_img_bgr = cv2.cvtColor(orig_img_rgb, cv2.COLOR_RGB2BGR)
    cv2.imwrite(orig_img_path, orig_img_bgr)
    # 保存 CAM 热力图
    heatmap_path = os.path.join(cam_path, filename.split('.')[0] + "D3_our.jpg")
    cv2.imwrite(heatmap_path, cam_image)
    print(f"✅ saved: {orig_img_path} and {heatmap_path}")


if __name__ == "__main__":
    txt_file = "./Food101.txt"
    with open(txt_file, "r") as f:
        image_paths = [line.strip().split(" ")[0] for line in f.readlines()]
    for image_path in image_paths:
        plot_cam(
            image_path=image_path,
            cam_path="./cam_results",
            flag=0
        )
