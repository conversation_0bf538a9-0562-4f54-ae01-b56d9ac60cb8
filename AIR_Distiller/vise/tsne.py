import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
import seaborn as sns
import random
from tqdm import tqdm

from AIR_Distiller.models import resnet18  # 请根据实际模型导入
from AIR_Distiller.dataloader.datasets.Food101 import Food101  # 请根据实际路径导入
from torch.utils.data import DataLoader
from torchvision import transforms

# 设定随机种子
random.seed(42)
torch.manual_seed(42)

# 超参数
ckpt_path = "../../log/D3_food101/Food101_D3_ResNet101_256x256_ResNet18_64x64/student_120.pth"
data_root = "/22zhangshijie/datasets/food-101"
num_classes = 101
num_per_class = 50
num_classes_to_sample = 10
img_size = 64

# 数据预处理
transform = transforms.Compose([
    transforms.Resize((img_size, img_size)),
    transforms.ToTensor(),
    transforms.Normalize([0.5]*3, [0.5]*3),
])

# 加载数据
dataset = Food101(root=data_root, transform=transform)
loader = DataLoader(dataset.gallery, batch_size=128, shuffle=False, num_workers=4)

# 加载模型
model = resnet18(pretrained=False, num_classes=num_classes, last_stride=1)
state_dict = torch.load(ckpt_path, map_location="cpu")
model.load_state_dict(state_dict, strict=False)
model.eval().cuda()

# 抽样类别
all_labels = [x[1] for x in dataset.gallery.samples]
unique_labels = sorted(list(set(all_labels)))
selected_classes = random.sample(unique_labels, num_classes_to_sample)

features = []
labels = []

# 特征提取
counts = {cls: 0 for cls in selected_classes}
with torch.no_grad():
    for imgs, targets, _ in tqdm(loader, desc="Extracting features"):
        imgs = imgs.cuda()
        outputs, feats = model(imgs)
        retrieval_feat = feats["retrieval_feat"].cpu().numpy()
        targets = targets.numpy()

        for feat, label in zip(retrieval_feat, targets):
            if label in selected_classes and counts[label] < num_per_class:
                features.append(feat)
                labels.append(label)
                counts[label] += 1

# 降维
features = np.array(features)
labels = np.array(labels)
tsne = TSNE(n_components=2, perplexity=30, learning_rate=200, n_iter=1000, random_state=42)
features_2d = tsne.fit_transform(features)

# 绘图
plt.figure(figsize=(8, 8))
palette = sns.color_palette("hls", num_classes_to_sample)
sns.scatterplot(x=features_2d[:, 0], y=features_2d[:, 1], hue=labels, palette=palette, s=12)
plt.title("t-SNE of Student Model Retrieval Features")
plt.legend(title="Class", bbox_to_anchor=(1.05, 1), loc='upper left')
plt.tight_layout()
plt.savefig("tsne_result.png", dpi=300)
plt.close()
print("✅ t-SNE 图像已保存为: tsne_result.png")