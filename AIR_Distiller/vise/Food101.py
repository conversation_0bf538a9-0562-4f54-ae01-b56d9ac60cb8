import os
import random

# 替换为你的图像根目录
data_root = "/22zhangshijie/datasets/food-101/data"
output_txt = "./Food101.txt"

# 随机种子保证复现
random.seed(42)

# 获取所有类别并随机选取10个
all_classes = sorted([d for d in os.listdir(data_root) if os.path.isdir(os.path.join(data_root, d))])
selected_classes = random.sample(all_classes, 10)

with open(output_txt, "w") as f:
    for cls_name in selected_classes:
        cls_path = os.path.join(data_root, cls_name)
        image_names = [img for img in os.listdir(cls_path) if img.endswith(".jpg") or img.endswith(".png")]
        selected_images = random.sample(image_names, min(30, len(image_names)))
        for img_name in selected_images:
            img_path = os.path.join(cls_path, img_name)
            line = f"{img_path} {cls_name}\n"
            f.write(line)

print(f"✅ 图像索引文件已保存至: {output_txt}")